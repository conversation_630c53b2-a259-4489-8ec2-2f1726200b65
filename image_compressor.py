#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩工具
支持批量压缩、压缩程度控制、格式转换
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from PIL import Image, ImageTk
import threading
from pathlib import Path
import time

class ImageCompressor:
    def __init__(self, root):
        self.root = root
        self.root.title("图片压缩工具 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 变量
        self.input_files = []
        self.output_folder = tk.StringVar()
        self.quality = tk.IntVar(value=80)
        self.output_format = tk.StringVar(value="JPEG")
        self.resize_enabled = tk.BooleanVar(value=False)
        self.resize_width = tk.IntVar(value=1920)
        self.resize_height = tk.IntVar(value=1080)
        self.keep_aspect_ratio = tk.BooleanVar(value=True)
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Button(file_frame, text="选择图片文件", command=self.select_files).grid(row=0, column=0, padx=(0, 10))
        self.file_count_label = ttk.Label(file_frame, text="未选择文件")
        self.file_count_label.grid(row=0, column=1, sticky=tk.W)
        
        # 文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        list_frame.columnconfigure(0, weight=1)
        
        self.file_listbox = tk.Listbox(list_frame, height=6)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 输出设置区域
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出文件夹:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(output_frame, textvariable=self.output_folder, state="readonly").grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="选择", command=self.select_output_folder).grid(row=0, column=2)
        
        ttk.Label(output_frame, text="输出格式:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        format_combo = ttk.Combobox(output_frame, textvariable=self.output_format, values=["JPEG", "PNG", "WEBP"], state="readonly")
        format_combo.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # 压缩设置区域
        compress_frame = ttk.LabelFrame(main_frame, text="压缩设置", padding="10")
        compress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        compress_frame.columnconfigure(1, weight=1)
        
        ttk.Label(compress_frame, text="压缩质量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        quality_frame = ttk.Frame(compress_frame)
        quality_frame.grid(row=0, column=1, sticky=(tk.W, tk.E))
        quality_frame.columnconfigure(0, weight=1)
        
        self.quality_scale = ttk.Scale(quality_frame, from_=10, to=100, variable=self.quality, orient=tk.HORIZONTAL)
        self.quality_scale.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        self.quality_label = ttk.Label(quality_frame, text=f"{self.quality.get()}%")
        self.quality_label.grid(row=0, column=1)
        
        self.quality_scale.configure(command=self.update_quality_label)
        
        # 尺寸调整区域
        resize_frame = ttk.LabelFrame(main_frame, text="尺寸调整", padding="10")
        resize_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Checkbutton(resize_frame, text="启用尺寸调整", variable=self.resize_enabled, command=self.toggle_resize).grid(row=0, column=0, columnspan=3, sticky=tk.W)
        
        ttk.Label(resize_frame, text="宽度:").grid(row=1, column=0, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.width_entry = ttk.Entry(resize_frame, textvariable=self.resize_width, width=10, state="disabled")
        self.width_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        ttk.Label(resize_frame, text="高度:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(10, 0))
        self.height_entry = ttk.Entry(resize_frame, textvariable=self.resize_height, width=10, state="disabled")
        self.height_entry.grid(row=1, column=3, sticky=tk.W, pady=(10, 0))
        
        self.aspect_ratio_check = ttk.Checkbutton(resize_frame, text="保持宽高比", variable=self.keep_aspect_ratio, state="disabled")
        self.aspect_ratio_check.grid(row=2, column=0, columnspan=4, sticky=tk.W, padx=(20, 0), pady=(10, 0))
        
        # 进度区域
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, text="准备就绪")
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(0, 10))
        
        self.start_button = ttk.Button(button_frame, text="开始压缩", command=self.start_compression, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空列表", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT)
        
    def select_files(self):
        """选择图片文件"""
        filetypes = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff *.webp"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="选择图片文件",
            filetypes=filetypes
        )
        
        if files:
            self.input_files.extend(files)
            self.update_file_list()
            
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.input_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)
        
        count = len(self.input_files)
        self.file_count_label.config(text=f"已选择 {count} 个文件")
        
    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder.set(folder)
            
    def clear_files(self):
        """清空文件列表"""
        self.input_files.clear()
        self.update_file_list()
        
    def update_quality_label(self, value):
        """更新质量标签"""
        self.quality_label.config(text=f"{int(float(value))}%")
        
    def toggle_resize(self):
        """切换尺寸调整功能"""
        state = "normal" if self.resize_enabled.get() else "disabled"
        self.width_entry.config(state=state)
        self.height_entry.config(state=state)
        self.aspect_ratio_check.config(state=state)
        
    def start_compression(self):
        """开始压缩处理"""
        if not self.input_files:
            messagebox.showwarning("警告", "请先选择图片文件！")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("警告", "请选择输出文件夹！")
            return
            
        # 在新线程中执行压缩
        self.start_button.config(state="disabled")
        thread = threading.Thread(target=self.compress_images)
        thread.daemon = True
        thread.start()
        
    def compress_images(self):
        """压缩图片的主要逻辑"""
        try:
            total_files = len(self.input_files)
            success_count = 0
            
            for i, input_path in enumerate(self.input_files):
                try:
                    # 更新状态
                    filename = os.path.basename(input_path)
                    self.root.after(0, lambda f=filename: self.status_label.config(text=f"正在处理: {f}"))
                    
                    # 打开图片
                    with Image.open(input_path) as img:
                        # 转换模式
                        if self.output_format.get() == "JPEG" and img.mode in ("RGBA", "P"):
                            img = img.convert("RGB")
                        elif self.output_format.get() == "PNG" and img.mode == "P":
                            img = img.convert("RGBA")
                            
                        # 尺寸调整
                        if self.resize_enabled.get():
                            new_width = self.resize_width.get()
                            new_height = self.resize_height.get()
                            
                            if self.keep_aspect_ratio.get():
                                img.thumbnail((new_width, new_height), Image.Resampling.LANCZOS)
                            else:
                                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                        
                        # 生成输出文件名
                        base_name = Path(input_path).stem
                        ext = ".jpg" if self.output_format.get() == "JPEG" else f".{self.output_format.get().lower()}"
                        output_path = os.path.join(self.output_folder.get(), f"{base_name}_compressed{ext}")
                        
                        # 保存图片
                        save_kwargs = {}
                        if self.output_format.get() in ["JPEG", "WEBP"]:
                            save_kwargs["quality"] = self.quality.get()
                            save_kwargs["optimize"] = True
                        
                        img.save(output_path, format=self.output_format.get(), **save_kwargs)
                        success_count += 1
                        
                except Exception as e:
                    print(f"处理文件 {input_path} 时出错: {str(e)}")
                    
                # 更新进度
                progress = (i + 1) / total_files * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                
            # 完成处理
            self.root.after(0, lambda: self.compression_completed(success_count, total_files))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"压缩过程中发生错误: {str(e)}"))
            self.root.after(0, lambda: self.start_button.config(state="normal"))
            
    def compression_completed(self, success_count, total_files):
        """压缩完成后的处理"""
        self.status_label.config(text=f"完成! 成功处理 {success_count}/{total_files} 个文件")
        self.start_button.config(state="normal")
        
        if success_count > 0:
            result = messagebox.askyesno("完成", f"成功压缩 {success_count} 个文件！\n是否打开输出文件夹？")
            if result:
                os.startfile(self.output_folder.get())

def main():
    root = tk.Tk()
    app = ImageCompressor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
