<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>压力监测数据可视化</title>
    <script src="http://www.resource.szwgft.cn/oss/echarts.min.js"></script>
    <script src="http://www.resource.szwgft.cn/oss/exceljs.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      #chart {
        width: 100%;
        height: 600px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .title {
        text-align: center;
        margin-bottom: 20px;
        color: #333;
      }
      .controls {
        text-align: center;
        margin-bottom: 20px;
      }
      .date-selector {
        padding: 8px 12px;
        font-size: 14px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
      }
      .date-selector:focus {
        outline: none;
        border-color: #5470c6;
      }
      .file-upload {
        margin-bottom: 20px;
        text-align: center;
      }
      .file-input {
        padding: 8px 12px;
        border: 2px dashed #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
        cursor: pointer;
        display: inline-block;
        margin-right: 10px;
      }
      .file-input:hover {
        border-color: #5470c6;
        background-color: #f0f8ff;
      }
      .upload-btn {
        padding: 8px 16px;
        background-color: #5470c6;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .upload-btn:hover {
        background-color: #4c63d2;
      }
      .status-message {
        margin-top: 10px;
        padding: 8px;
        border-radius: 4px;
        display: none;
      }
      .status-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
    </style>
  </head>

  <body>
    <h1 class="title">压力监测数据可视化</h1>

    <div class="file-upload">
      <input type="file" id="fileInput" accept=".xlsx,.xls" class="file-input" />
      <button onclick="uploadFile()" class="upload-btn">上传Excel文件</button>
      <div id="statusMessage" class="status-message"></div>
    </div>

    <div class="controls">
      <label for="dateSelector">选择工作表：</label>
      <select id="dateSelector" class="date-selector">
        <option value="">请先上传Excel文件</option>
      </select>
    </div>
    <div id="chart"></div>

    <script>
      // 存储从Excel文件读取的数据
      let excelData = {}

      // 上传Excel文件
      function uploadFile() {
        const fileInput = document.getElementById('fileInput')
        const file = fileInput.files[0]
        const statusMessage = document.getElementById('statusMessage')

        if (!file) {
          showMessage('请选择一个Excel文件', 'error')
          return
        }

        if (!file.name.match(/\.(xlsx|xls)$/)) {
          showMessage('请选择有效的Excel文件(.xlsx或.xls)', 'error')
          return
        }

        showMessage('正在读取文件...', 'success')

        const reader = new FileReader()
        reader.onload = async function (e) {
          try {
            const buffer = e.target.result
            const workbook = new ExcelJS.Workbook()
            await workbook.xlsx.load(buffer)

            // 清空之前的数据
            excelData = {}

            // 读取每个工作表
            workbook.worksheets.forEach((worksheet) => {
              const sheetName = worksheet.name

              if (worksheet.rowCount > 1) {
                // 获取第一行作为表头
                const headerRow = worksheet.getRow(1)
                const headers = []
                headerRow.eachCell((cell, colNumber) => {
                  headers[colNumber - 1] = cell.value
                })

                // 精确匹配列名
                const timeIndex = headers.findIndex((h) => {
                  if (!h) return false
                  const headerStr = h.toString().trim()
                  return headerStr === '采集时间' || headerStr.includes('采集时间') || headerStr.includes('时间')
                })

                const outletIndex = headers.findIndex((h) => {
                  if (!h) return false
                  const headerStr = h.toString().trim()
                  return headerStr === '出口压力' || headerStr.includes('出口压力')
                })

                const endIndex = headers.findIndex((h) => {
                  if (!h) return false
                  const headerStr = h.toString().trim()
                  return headerStr === '末端压力' || headerStr.includes('末端压力')
                })

                if (timeIndex !== -1 && outletIndex !== -1 && endIndex !== -1) {
                  const times = []
                  const outletPressures = []
                  const endPressures = []

                  // 从第二行开始读取数据
                  for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
                    const row = worksheet.getRow(rowNumber)

                    const timeCell = row.getCell(timeIndex + 1)
                    const outletCell = row.getCell(outletIndex + 1)
                    const endCell = row.getCell(endIndex + 1)

                    if (timeCell.value && outletCell.value !== undefined && endCell.value !== undefined) {
                      // 处理时间格式
                      let timeStr = timeCell.value

                      if (timeCell.value instanceof Date) {
                        // ExcelJS已经将日期转换为Date对象
                        timeStr = timeCell.value.toLocaleString('zh-CN')
                      } else if (typeof timeStr === 'string') {
                        timeStr = timeStr.trim()
                      } else if (typeof timeStr === 'number') {
                        // 如果还是数字，手动转换
                        const date = new Date((timeStr - 25569) * 86400 * 1000)
                        timeStr = date.toLocaleString('zh-CN')
                      }

                      // 处理压力数据
                      const outletValue = parseFloat(outletCell.value)
                      const endValue = parseFloat(endCell.value)

                      // 只有当数据有效时才添加
                      if (!isNaN(outletValue) && !isNaN(endValue) && timeStr) {
                        times.push(timeStr)
                        outletPressures.push(outletValue)
                        endPressures.push(endValue)
                      }
                    }
                  }

                  if (times.length > 0) {
                    excelData[sheetName] = {
                      采集时间: times,
                      出口压力: outletPressures,
                      末端压力: endPressures
                    }
                  }
                }
              }
            })

            if (Object.keys(excelData).length > 0) {
              const totalDataPoints = Object.values(excelData).reduce((sum, sheet) => sum + sheet.采集时间.length, 0)
              showMessage(`成功读取 ${Object.keys(excelData).length} 个工作表，共 ${totalDataPoints} 条数据`, 'success')
              updateSheetSelector()

              // 默认选择第一个工作表
              const firstSheet = Object.keys(excelData)[0]
              document.getElementById('dateSelector').value = firstSheet
              updateChart(firstSheet)
            } else {
              showMessage('未找到有效的数据。请确保Excel文件包含"采集时间"、"出口压力"、"末端压力"列，且数据格式正确', 'error')
            }
          } catch (error) {
            console.error('读取文件错误:', error)
            showMessage('文件读取失败，请检查文件格式', 'error')
          }
        }

        reader.readAsArrayBuffer(file)
      }

      // 显示状态消息
      function showMessage(message, type) {
        const statusMessage = document.getElementById('statusMessage')
        statusMessage.textContent = message
        statusMessage.className = `status-message status-${type}`
        statusMessage.style.display = 'block'

        if (type === 'success') {
          setTimeout(() => {
            statusMessage.style.display = 'none'
          }, 3000)
        }
      }

      // 更新工作表选择器
      function updateSheetSelector() {
        const selector = document.getElementById('dateSelector')
        selector.innerHTML = '<option value="">请选择工作表</option>'

        Object.keys(excelData).forEach((sheetName, index) => {
          const option = document.createElement('option')
          option.value = sheetName
          option.textContent = sheetName
          // 默认选中第一个工作表
          if (index === 0) {
            option.selected = true
          }
          selector.appendChild(option)
        })
      }

      // 原有的示例数据（作为备用）
      const newestData = {
        '2025-09-05': {
          采集时间: ['2025-09-05 00:00:10', '2025-09-05 00:00:20', '2025-09-05 00:00:30'],
          出口压力: [1.0392, 1.0374, 1.0485],
          末端压力: [0.302, 0.302, 0.302]
        }
      }

      const myChart = echarts.init(document.getElementById('chart'))

      const option = {
        title: {
          text: '出口压力与末端压力对比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['出口压力', '末端压力'],
          top: 30
        },
        toolbox: {
          show: true,
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value',
          name: '压力 (MPa)',
          axisLabel: {
            formatter: '{value}'
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ],
        series: [
          {
            name: '出口压力',
            type: 'line',
            smooth: true,
            data: [],
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '末端压力',
            type: 'line',
            smooth: true,
            data: [],
            itemStyle: {
              color: '#91cc75'
            }
          }
        ]
      }

      // 找到数组中的最大值和最小值及其索引
      function findExtremeValues(data, times) {
        const maxValue = Math.max(...data)
        const minValue = Math.min(...data)
        const maxIndex = data.indexOf(maxValue)
        const minIndex = data.indexOf(minValue)

        return {
          max: { value: maxValue, index: maxIndex, time: times[maxIndex] },
          min: { value: minValue, index: minIndex, time: times[minIndex] }
        }
      }

      // 处理数据并更新图表
      function updateChart(selectedSheet) {
        const dataSource = Object.keys(excelData).length > 0 ? excelData : newestData

        if (!selectedSheet || !dataSource[selectedSheet]) {
          // 如果没有选择工作表，清空图表
          option.xAxis.data = []
          option.series[0].data = []
          option.series[1].data = []
          option.series[2] = undefined
          option.series[3] = undefined
          myChart.setOption(option)
          return
        }

        const data = dataSource[selectedSheet]

        // 处理时间数据
        let times = data.采集时间
        if (typeof times[0] === 'string' && times[0].includes(' ')) {
          // 如果是完整的日期时间，提取时分秒部分
          times = times.map((time) => {
            const parts = time.split(' ')
            return parts.length > 1 ? parts[1] : time
          })
        }

        // 获取压力数据
        const outletPressure = data.出口压力
        const endPressure = data.末端压力

        // 找到出口压力和末端压力的极值
        const outletExtremes = findExtremeValues(outletPressure, times)
        const endExtremes = findExtremeValues(endPressure, times)

        // 更新图表配置
        option.xAxis.data = times
        option.series[0].data = outletPressure
        option.series[1].data = endPressure

        // 添加标记最高点和最低点的散点图系列
        option.series[2] = {
          name: '极值点',
          type: 'scatter',
          data: [
            {
              value: [outletExtremes.max.index, outletExtremes.max.value],
              symbol: 'triangle',
              symbolSize: 12,
              itemStyle: { color: '#ff4757' },
              label: {
                show: true,
                position: 'top',
                formatter: `最高: ${outletExtremes.max.value}`,
                fontSize: 10,
                color: '#ff4757'
              }
            },
            {
              value: [outletExtremes.min.index, outletExtremes.min.value],
              symbol: 'triangle',
              symbolSize: 12,
              symbolRotate: 180,
              itemStyle: { color: '#2f3542' },
              label: {
                show: true,
                position: 'bottom',
                formatter: `最低: ${outletExtremes.min.value}`,
                fontSize: 10,
                color: '#2f3542'
              }
            }
          ],
          tooltip: {
            formatter: function (params) {
              const isMax = params.data.itemStyle.color === '#ff4757'
              const type = isMax ? '最高点' : '最低点'
              return `出口压力${type}<br/>时间: ${times[params.data.value[0]]}<br/>压力: ${params.data.value[1]} MPa`
            }
          }
        }

        option.series[3] = {
          name: '极值点',
          type: 'scatter',
          data: [
            {
              value: [endExtremes.max.index, endExtremes.max.value],
              symbol: 'triangle',
              symbolSize: 12,
              itemStyle: { color: '#ff6b81' },
              label: {
                show: true,
                position: 'top',
                formatter: `最高: ${endExtremes.max.value}`,
                fontSize: 10,
                color: '#ff6b81'
              }
            },
            {
              value: [endExtremes.min.index, endExtremes.min.value],
              symbol: 'triangle',
              symbolSize: 12,
              symbolRotate: 180,
              itemStyle: { color: '#57606f' },
              label: {
                show: true,
                position: 'bottom',
                formatter: `最低: ${endExtremes.min.value}`,
                fontSize: 10,
                color: '#57606f'
              }
            }
          ],
          tooltip: {
            formatter: function (params) {
              const isMax = params.data.itemStyle.color === '#ff6b81'
              const type = isMax ? '最高点' : '最低点'
              return `末端压力${type}<br/>时间: ${times[params.data.value[0]]}<br/>压力: ${params.data.value[1]} MPa`
            }
          }
        }

        // 更新图表标题显示选中的工作表
        option.title.text = `出口压力与末端压力对比 (${selectedSheet})`

        myChart.setOption(option)
      }

      // 监听下拉框变化
      document.getElementById('dateSelector').addEventListener('change', function (e) {
        updateChart(e.target.value)
      })

      // 初始化
      myChart.setOption(option)

      // 如果没有Excel数据，默认展示示例数据
      if (Object.keys(excelData).length === 0) {
        const firstDate = Object.keys(newestData)[0]
        if (firstDate) {
          updateChart(firstDate)
        }
      }

      // 响应式调整
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    </script>
  </body>
</html>
