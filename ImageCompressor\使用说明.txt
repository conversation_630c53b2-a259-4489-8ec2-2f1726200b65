图片压缩工具 v1.0 使用说明
================================

📁 文件说明
-----------
image_compressor.py  - 主程序文件
requirements.txt     - 依赖包列表
build.py            - Python打包脚本
build.bat           - Windows批处理打包脚本
README.md           - 详细说明文档
使用说明.txt         - 本文件

🚀 快速开始
-----------

方法一：直接运行（需要Python环境）
1. 安装依赖：pip install -r requirements.txt
2. 运行程序：python image_compressor.py

方法二：打包成exe文件
1. 双击运行 build.bat
2. 等待打包完成
3. 在 dist 文件夹中找到 图片压缩工具.exe

📋 主要功能
-----------
✅ 批量压缩图片文件
✅ 压缩质量控制（10%-100%）
✅ 图片格式转换（JPEG/PNG/WEBP）
✅ 尺寸调整功能
✅ 保持宽高比选项
✅ 实时进度显示
✅ 友好的图形界面

🖼️ 支持格式
-----------
输入：JPEG, PNG, BMP, GIF, TIFF, WEBP
输出：JPEG, PNG, WEBP

🎯 使用步骤
-----------
1. 点击"选择图片文件"选择要压缩的图片
2. 点击"选择"设置输出文件夹
3. 选择输出格式（JPEG/PNG/WEBP）
4. 调整压缩质量（推荐70-85%）
5. 可选：启用尺寸调整
6. 点击"开始压缩"

⚙️ 压缩建议
-----------
照片压缩：JPEG格式，70-85%质量
网页图片：WEBP格式，60-80%质量
图标Logo：PNG格式，90-100%质量

⚠️ 注意事项
-----------
• 输出文件名会添加"_compressed"后缀
• JPEG格式不支持透明度
• 建议先备份原文件
• 大文件处理需要时间，请耐心等待

🔧 系统要求
-----------
• Windows 7/8/10/11
• exe版本无需Python环境
• 源码版本需要Python 3.7+

📞 技术支持
-----------
如有问题请查看 README.md 详细文档
或检查依赖包是否正确安装

版本：1.0
开发：Python + tkinter + Pillow
