#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化图片压缩工具
支持批量压缩、压缩程度控制、格式转换
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from PIL import Image, ImageTk
import threading
from pathlib import Path
import time

class ModernImageCompressor:
    def __init__(self, root):
        self.root = root
        self.root.title("现代化图片压缩工具 v2.0")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 设置现代化配色
        self.colors = {
            'primary': '#2563eb',      # 蓝色主色调
            'primary_dark': '#1d4ed8', # 深蓝色
            'secondary': '#64748b',    # 灰蓝色
            'success': '#10b981',      # 绿色
            'warning': '#f59e0b',      # 橙色
            'danger': '#ef4444',       # 红色
            'background': '#f8fafc',   # 浅灰背景
            'surface': '#ffffff',      # 白色表面
            'text': '#1e293b',         # 深色文字
            'text_light': '#64748b',   # 浅色文字
            'border': '#e2e8f0'        # 边框色
        }

        # 设置窗口背景
        self.root.configure(bg=self.colors['background'])

        # 配置现代化样式
        self.setup_modern_styles()
        
        # 变量
        self.input_files = []
        self.output_folder = tk.StringVar()
        self.quality = tk.IntVar(value=80)
        self.output_format = tk.StringVar(value="JPEG")
        self.resize_enabled = tk.BooleanVar(value=False)
        self.resize_width = tk.IntVar(value=1920)
        self.resize_height = tk.IntVar(value=1080)
        self.keep_aspect_ratio = tk.BooleanVar(value=True)

        self.setup_ui()

    def setup_modern_styles(self):
        """配置现代化样式"""
        style = ttk.Style()

        # 配置主题样式
        style.configure('Modern.TFrame',
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)

        style.configure('Card.TFrame',
                       background=self.colors['surface'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['border'])

        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_dark'])])

        style.configure('Secondary.TButton',
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(15, 8))

        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.configure('Modern.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=('Segoe UI', 10))

        style.configure('Title.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=('Segoe UI', 14, 'bold'))

        style.configure('Subtitle.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['text_light'],
                       font=('Segoe UI', 9))

        style.configure('Modern.TLabelFrame',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       borderwidth=1,
                       relief='solid',
                       bordercolor=self.colors['border'],
                       font=('Segoe UI', 11, 'bold'))

        style.configure('Modern.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['border'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])
        
    def setup_ui(self):
        # 创建主容器
        main_container = tk.Frame(self.root, bg=self.colors['background'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题区域
        self.create_header(main_container)

        # 创建滚动区域
        self.create_scrollable_content(main_container)

    def create_header(self, parent):
        """创建现代化标题区域"""
        header_frame = tk.Frame(parent, bg=self.colors['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = tk.Label(header_frame,
                              text="🖼️ 现代化图片压缩工具",
                              font=('Segoe UI', 24, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['background'])
        title_label.pack(anchor=tk.W)

        # 副标题
        subtitle_label = tk.Label(header_frame,
                                 text="批量压缩、格式转换、尺寸调整 - 一站式图片处理解决方案",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['text_light'],
                                 bg=self.colors['background'])
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))

    def create_scrollable_content(self, parent):
        """创建可滚动的内容区域"""
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(parent, bg=self.colors['background'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 创建内容区域
        self.create_content_sections(scrollable_frame)

        # 鼠标滚轮绑定
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
    def create_content_sections(self, parent):
        """创建内容区域的各个部分"""
        # 文件选择卡片
        self.create_file_selection_card(parent)

        # 设置卡片
        self.create_settings_card(parent)

        # 进度卡片
        self.create_progress_card(parent)

        # 控制按钮
        self.create_control_buttons(parent)

    def create_file_selection_card(self, parent):
        """创建文件选择卡片"""
        card_frame = self.create_card(parent, "📁 文件选择")

        # 选择文件按钮区域
        button_frame = tk.Frame(card_frame, bg=self.colors['surface'])
        button_frame.pack(fill=tk.X, pady=(0, 15))

        select_btn = tk.Button(button_frame,
                              text="📂 选择图片文件",
                              command=self.select_files,
                              bg=self.colors['primary'],
                              fg='white',
                              font=('Segoe UI', 11, 'bold'),
                              relief='flat',
                              padx=20,
                              pady=10,
                              cursor='hand2')
        select_btn.pack(side=tk.LEFT)

        # 文件计数标签
        self.file_count_label = tk.Label(button_frame,
                                        text="未选择文件",
                                        font=('Segoe UI', 10),
                                        fg=self.colors['text_light'],
                                        bg=self.colors['surface'])
        self.file_count_label.pack(side=tk.LEFT, padx=(15, 0))

        # 文件列表区域
        list_container = tk.Frame(card_frame, bg=self.colors['surface'])
        list_container.pack(fill=tk.BOTH, expand=True)

        # 创建现代化列表框
        self.file_listbox = tk.Listbox(list_container,
                                      height=6,
                                      font=('Segoe UI', 10),
                                      bg='white',
                                      fg=self.colors['text'],
                                      selectbackground=self.colors['primary'],
                                      selectforeground='white',
                                      relief='solid',
                                      borderwidth=1,
                                      highlightthickness=0)
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        list_scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.file_listbox.yview)
        list_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.file_listbox.configure(yscrollcommand=list_scrollbar.set)
        
    def create_card(self, parent, title):
        """创建现代化卡片容器"""
        # 卡片外框
        card_container = tk.Frame(parent, bg=self.colors['background'])
        card_container.pack(fill=tk.X, pady=(0, 20))

        # 卡片标题
        title_frame = tk.Frame(card_container, bg=self.colors['background'])
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = tk.Label(title_frame,
                              text=title,
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['text'],
                              bg=self.colors['background'])
        title_label.pack(anchor=tk.W)

        # 卡片内容区域
        card_frame = tk.Frame(card_container,
                             bg=self.colors['surface'],
                             relief='solid',
                             borderwidth=1,
                             highlightbackground=self.colors['border'],
                             highlightthickness=1)
        card_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 内边距
        content_frame = tk.Frame(card_frame, bg=self.colors['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        return content_frame

    def create_settings_card(self, parent):
        """创建设置卡片"""
        card_frame = self.create_card(parent, "⚙️ 压缩设置")

        # 输出设置区域
        output_section = tk.Frame(card_frame, bg=self.colors['surface'])
        output_section.pack(fill=tk.X, pady=(0, 20))

        # 输出文件夹
        folder_frame = tk.Frame(output_section, bg=self.colors['surface'])
        folder_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(folder_frame,
                text="📁 输出文件夹",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['surface']).pack(anchor=tk.W)

        folder_input_frame = tk.Frame(folder_frame, bg=self.colors['surface'])
        folder_input_frame.pack(fill=tk.X, pady=(8, 0))

        self.folder_entry = tk.Entry(folder_input_frame,
                                    textvariable=self.output_folder,
                                    font=('Segoe UI', 10),
                                    relief='solid',
                                    borderwidth=1,
                                    state="readonly")
        self.folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        folder_btn = tk.Button(folder_input_frame,
                              text="选择文件夹",
                              command=self.select_output_folder,
                              bg=self.colors['secondary'],
                              fg='white',
                              font=('Segoe UI', 10),
                              relief='flat',
                              padx=15,
                              pady=8,
                              cursor='hand2')
        folder_btn.pack(side=tk.RIGHT)

        # 输出格式
        format_frame = tk.Frame(output_section, bg=self.colors['surface'])
        format_frame.pack(fill=tk.X)

        tk.Label(format_frame,
                text="🎨 输出格式",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['surface']).pack(anchor=tk.W)

        format_buttons_frame = tk.Frame(format_frame, bg=self.colors['surface'])
        format_buttons_frame.pack(fill=tk.X, pady=(8, 0))

        # 格式选择按钮
        self.format_buttons = {}
        formats = [("JPEG", "📷"), ("PNG", "🖼️"), ("WEBP", "🌐")]

        for i, (fmt, icon) in enumerate(formats):
            btn = tk.Button(format_buttons_frame,
                           text=f"{icon} {fmt}",
                           command=lambda f=fmt: self.select_format(f),
                           font=('Segoe UI', 10),
                           relief='solid',
                           borderwidth=1,
                           padx=20,
                           pady=8,
                           cursor='hand2')
            btn.pack(side=tk.LEFT, padx=(0, 10) if i < len(formats)-1 else 0)
            self.format_buttons[fmt] = btn

        # 初始化格式选择
        self.select_format("JPEG")
        
        # 压缩质量控制
        quality_section = tk.Frame(card_frame, bg=self.colors['surface'])
        quality_section.pack(fill=tk.X, pady=(20, 20))

        tk.Label(quality_section,
                text="🎚️ 压缩质量",
                font=('Segoe UI', 11, 'bold'),
                fg=self.colors['text'],
                bg=self.colors['surface']).pack(anchor=tk.W)

        quality_control_frame = tk.Frame(quality_section, bg=self.colors['surface'])
        quality_control_frame.pack(fill=tk.X, pady=(8, 0))

        # 质量滑块
        self.quality_scale = tk.Scale(quality_control_frame,
                                     from_=10, to=100,
                                     variable=self.quality,
                                     orient=tk.HORIZONTAL,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'],
                                     highlightthickness=0,
                                     troughcolor=self.colors['border'],
                                     activebackground=self.colors['primary'],
                                     font=('Segoe UI', 10),
                                     command=self.update_quality_label)
        self.quality_scale.pack(fill=tk.X, pady=(0, 10))

        # 质量标签和预设按钮
        quality_info_frame = tk.Frame(quality_control_frame, bg=self.colors['surface'])
        quality_info_frame.pack(fill=tk.X)

        self.quality_label = tk.Label(quality_info_frame,
                                     text=f"当前质量: {self.quality.get()}%",
                                     font=('Segoe UI', 10, 'bold'),
                                     fg=self.colors['primary'],
                                     bg=self.colors['surface'])
        self.quality_label.pack(side=tk.LEFT)

        # 质量预设按钮
        preset_frame = tk.Frame(quality_info_frame, bg=self.colors['surface'])
        preset_frame.pack(side=tk.RIGHT)

        presets = [("高质量", 90), ("平衡", 75), ("小文件", 50)]
        for name, value in presets:
            btn = tk.Button(preset_frame,
                           text=name,
                           command=lambda v=value: self.set_quality_preset(v),
                           bg=self.colors['border'],
                           fg=self.colors['text'],
                           font=('Segoe UI', 9),
                           relief='flat',
                           padx=10,
                           pady=4,
                           cursor='hand2')
            btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 尺寸调整区域
        resize_section = tk.Frame(card_frame, bg=self.colors['surface'])
        resize_section.pack(fill=tk.X, pady=(20, 0))

        # 尺寸调整开关
        resize_header_frame = tk.Frame(resize_section, bg=self.colors['surface'])
        resize_header_frame.pack(fill=tk.X, pady=(0, 10))

        self.resize_check = tk.Checkbutton(resize_header_frame,
                                          text="📏 启用尺寸调整",
                                          variable=self.resize_enabled,
                                          command=self.toggle_resize,
                                          font=('Segoe UI', 11, 'bold'),
                                          fg=self.colors['text'],
                                          bg=self.colors['surface'],
                                          activebackground=self.colors['surface'],
                                          selectcolor=self.colors['primary'])
        self.resize_check.pack(anchor=tk.W)

        # 尺寸输入区域
        self.resize_controls_frame = tk.Frame(resize_section, bg=self.colors['surface'])
        self.resize_controls_frame.pack(fill=tk.X, padx=(20, 0))

        # 宽度和高度输入
        dimensions_frame = tk.Frame(self.resize_controls_frame, bg=self.colors['surface'])
        dimensions_frame.pack(fill=tk.X, pady=(0, 10))

        # 宽度
        width_frame = tk.Frame(dimensions_frame, bg=self.colors['surface'])
        width_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(width_frame,
                text="宽度",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['surface']).pack(anchor=tk.W)

        self.width_entry = tk.Entry(width_frame,
                                   textvariable=self.resize_width,
                                   width=10,
                                   font=('Segoe UI', 10),
                                   relief='solid',
                                   borderwidth=1,
                                   state="disabled")
        self.width_entry.pack(pady=(5, 0))

        # 高度
        height_frame = tk.Frame(dimensions_frame, bg=self.colors['surface'])
        height_frame.pack(side=tk.LEFT)

        tk.Label(height_frame,
                text="高度",
                font=('Segoe UI', 10),
                fg=self.colors['text'],
                bg=self.colors['surface']).pack(anchor=tk.W)

        self.height_entry = tk.Entry(height_frame,
                                    textvariable=self.resize_height,
                                    width=10,
                                    font=('Segoe UI', 10),
                                    relief='solid',
                                    borderwidth=1,
                                    state="disabled")
        self.height_entry.pack(pady=(5, 0))

        # 保持宽高比选项
        self.aspect_ratio_check = tk.Checkbutton(self.resize_controls_frame,
                                                text="保持宽高比",
                                                variable=self.keep_aspect_ratio,
                                                font=('Segoe UI', 10),
                                                fg=self.colors['text'],
                                                bg=self.colors['surface'],
                                                activebackground=self.colors['surface'],
                                                selectcolor=self.colors['primary'],
                                                state="disabled")
        self.aspect_ratio_check.pack(anchor=tk.W)
        
    def create_progress_card(self, parent):
        """创建进度显示卡片"""
        card_frame = self.create_card(parent, "📊 处理进度")

        # 进度条
        self.progress_var = tk.DoubleVar()
        progress_container = tk.Frame(card_frame, bg=self.colors['surface'])
        progress_container.pack(fill=tk.X, pady=(0, 15))

        # 进度条样式
        progress_canvas = tk.Canvas(progress_container,
                                   height=20,
                                   bg=self.colors['border'],
                                   highlightthickness=0)
        progress_canvas.pack(fill=tk.X)

        self.progress_canvas = progress_canvas
        self.progress_rect = progress_canvas.create_rectangle(0, 0, 0, 20,
                                                             fill=self.colors['primary'],
                                                             outline="")

        # 状态标签
        self.status_label = tk.Label(card_frame,
                                    text="🟢 准备就绪",
                                    font=('Segoe UI', 11),
                                    fg=self.colors['text'],
                                    bg=self.colors['surface'])
        self.status_label.pack(anchor=tk.W)

        # 统计信息
        self.stats_label = tk.Label(card_frame,
                                   text="",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['text_light'],
                                   bg=self.colors['surface'])
        self.stats_label.pack(anchor=tk.W, pady=(5, 0))

    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_container = tk.Frame(parent, bg=self.colors['background'])
        button_container.pack(fill=tk.X, pady=(20, 0))

        # 主要操作按钮
        main_buttons_frame = tk.Frame(button_container, bg=self.colors['background'])
        main_buttons_frame.pack(fill=tk.X)

        # 开始压缩按钮
        self.start_button = tk.Button(main_buttons_frame,
                                     text="🚀 开始压缩",
                                     command=self.start_compression,
                                     bg=self.colors['success'],
                                     fg='white',
                                     font=('Segoe UI', 12, 'bold'),
                                     relief='flat',
                                     padx=30,
                                     pady=12,
                                     cursor='hand2')
        self.start_button.pack(side=tk.LEFT, padx=(0, 15))

        # 清空列表按钮
        clear_button = tk.Button(main_buttons_frame,
                                text="🗑️ 清空列表",
                                command=self.clear_files,
                                bg=self.colors['warning'],
                                fg='white',
                                font=('Segoe UI', 11),
                                relief='flat',
                                padx=20,
                                pady=10,
                                cursor='hand2')
        clear_button.pack(side=tk.LEFT, padx=(0, 15))

        # 退出按钮
        exit_button = tk.Button(main_buttons_frame,
                               text="❌ 退出",
                               command=self.root.quit,
                               bg=self.colors['danger'],
                               fg='white',
                               font=('Segoe UI', 11),
                               relief='flat',
                               padx=20,
                               pady=10,
                               cursor='hand2')
        exit_button.pack(side=tk.RIGHT)
        
    def select_files(self):
        """选择图片文件"""
        filetypes = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff *.webp"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="选择图片文件",
            filetypes=filetypes
        )
        
        if files:
            self.input_files.extend(files)
            self.update_file_list()
            
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.input_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)
        
        count = len(self.input_files)
        self.file_count_label.config(text=f"已选择 {count} 个文件")
        
    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder.set(folder)
            
    def clear_files(self):
        """清空文件列表"""
        self.input_files.clear()
        self.update_file_list()
        
    def select_format(self, format_name):
        """选择输出格式"""
        self.output_format.set(format_name)

        # 更新按钮样式
        for fmt, btn in self.format_buttons.items():
            if fmt == format_name:
                btn.config(bg=self.colors['primary'], fg='white')
            else:
                btn.config(bg=self.colors['border'], fg=self.colors['text'])

    def set_quality_preset(self, value):
        """设置质量预设值"""
        self.quality.set(value)
        self.update_quality_label(value)

    def update_quality_label(self, value):
        """更新质量标签"""
        quality_val = int(float(value))
        self.quality_label.config(text=f"当前质量: {quality_val}%")

        # 根据质量值更改颜色
        if quality_val >= 80:
            color = self.colors['success']
            desc = "高质量"
        elif quality_val >= 60:
            color = self.colors['warning']
            desc = "平衡"
        else:
            color = self.colors['danger']
            desc = "小文件"

        self.quality_label.config(fg=color)

    def update_progress(self, value):
        """更新进度条"""
        if hasattr(self, 'progress_canvas'):
            canvas_width = self.progress_canvas.winfo_width()
            if canvas_width > 1:  # 确保canvas已经渲染
                progress_width = (value / 100) * canvas_width
                self.progress_canvas.coords(self.progress_rect, 0, 0, progress_width, 20)
        
    def toggle_resize(self):
        """切换尺寸调整功能"""
        state = "normal" if self.resize_enabled.get() else "disabled"
        self.width_entry.config(state=state)
        self.height_entry.config(state=state)
        self.aspect_ratio_check.config(state=state)
        
    def start_compression(self):
        """开始压缩处理"""
        if not self.input_files:
            messagebox.showwarning("警告", "请先选择图片文件！")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("警告", "请选择输出文件夹！")
            return
            
        # 在新线程中执行压缩
        self.start_button.config(state="disabled", text="🔄 处理中...")
        self.status_label.config(text="🔄 准备开始处理...")
        self.stats_label.config(text="")
        thread = threading.Thread(target=self.compress_images)
        thread.daemon = True
        thread.start()
        
    def compress_images(self):
        """压缩图片的主要逻辑"""
        try:
            total_files = len(self.input_files)
            success_count = 0
            
            for i, input_path in enumerate(self.input_files):
                try:
                    # 更新状态
                    filename = os.path.basename(input_path)
                    self.root.after(0, lambda f=filename: self.status_label.config(text=f"🔄 正在处理: {f}"))
                    
                    # 打开图片
                    with Image.open(input_path) as img:
                        # 转换模式
                        if self.output_format.get() == "JPEG" and img.mode in ("RGBA", "P"):
                            img = img.convert("RGB")
                        elif self.output_format.get() == "PNG" and img.mode == "P":
                            img = img.convert("RGBA")
                            
                        # 尺寸调整
                        if self.resize_enabled.get():
                            new_width = self.resize_width.get()
                            new_height = self.resize_height.get()
                            
                            if self.keep_aspect_ratio.get():
                                img.thumbnail((new_width, new_height), Image.Resampling.LANCZOS)
                            else:
                                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                        
                        # 生成输出文件名
                        base_name = Path(input_path).stem
                        ext = ".jpg" if self.output_format.get() == "JPEG" else f".{self.output_format.get().lower()}"
                        output_path = os.path.join(self.output_folder.get(), f"{base_name}_compressed{ext}")
                        
                        # 保存图片
                        save_kwargs = {}
                        if self.output_format.get() in ["JPEG", "WEBP"]:
                            save_kwargs["quality"] = self.quality.get()
                            save_kwargs["optimize"] = True
                        
                        img.save(output_path, format=self.output_format.get(), **save_kwargs)
                        success_count += 1
                        
                except Exception as e:
                    print(f"处理文件 {input_path} 时出错: {str(e)}")
                    
                # 更新进度
                progress = (i + 1) / total_files * 100
                self.root.after(0, lambda p=progress: self.update_progress(p))

                # 更新统计信息
                self.root.after(0, lambda i=i+1, t=total_files, s=success_count:
                               self.stats_label.config(text=f"已处理: {i}/{t} 文件，成功: {s} 个"))
                
            # 完成处理
            self.root.after(0, lambda: self.compression_completed(success_count, total_files))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"压缩过程中发生错误: {str(e)}"))
            self.root.after(0, lambda: self.start_button.config(state="normal"))
            
    def compression_completed(self, success_count, total_files):
        """压缩完成后的处理"""
        if success_count == total_files:
            self.status_label.config(text=f"✅ 完成! 成功处理 {success_count}/{total_files} 个文件")
        else:
            self.status_label.config(text=f"⚠️ 完成! 成功处理 {success_count}/{total_files} 个文件")

        self.stats_label.config(text=f"处理完成，成功率: {success_count/total_files*100:.1f}%")
        self.start_button.config(state="normal", text="🚀 开始压缩")

        if success_count > 0:
            result = messagebox.askyesno("处理完成",
                                       f"✅ 成功压缩 {success_count} 个文件！\n\n是否打开输出文件夹查看结果？",
                                       icon='question')
            if result:
                os.startfile(self.output_folder.get())

def main():
    root = tk.Tk()
    app = ModernImageCompressor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
