#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩工具打包脚本
使用PyInstaller将Python脚本打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def build_exe():
    """使用PyInstaller打包exe文件"""
    print("开始打包exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 不显示控制台窗口
        "--name=图片压缩工具",           # 设置exe文件名
        "--icon=icon.ico",              # 图标文件（如果存在）
        "--add-data=README.md;.",       # 添加说明文件
        "--distpath=dist",              # 输出目录
        "--workpath=build",             # 临时文件目录
        "--specpath=.",                 # spec文件位置
        "image_compressor.py"           # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("icon.ico"):
        cmd = [arg for arg in cmd if not arg.startswith("--icon")]
    
    # 如果没有README文件，移除该参数
    if not os.path.exists("README.md"):
        cmd = [arg for arg in cmd if not arg.startswith("--add-data")]
    
    try:
        subprocess.check_call(cmd)
        print("打包完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller未找到，请先安装: pip install pyinstaller")
        return False

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("清理临时文件...")
    
    # 要删除的目录和文件
    to_remove = ["build", "__pycache__", "图片压缩工具.spec"]
    
    for item in to_remove:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"删除目录: {item}")
            else:
                os.remove(item)
                print(f"删除文件: {item}")

def create_readme():
    """创建说明文件"""
    readme_content = """# 图片压缩工具

## 功能特点

### 🖼️ 支持的图片格式
- 输入格式：JPEG, PNG, BMP, GIF, TIFF, WEBP
- 输出格式：JPEG, PNG, WEBP

### ⚙️ 主要功能
1. **批量压缩**：一次性处理多个图片文件
2. **压缩质量控制**：10%-100%可调节压缩质量
3. **格式转换**：支持不同图片格式之间的转换
4. **尺寸调整**：可选择调整图片尺寸
5. **保持宽高比**：调整尺寸时可保持原始宽高比
6. **进度显示**：实时显示处理进度
7. **可视化界面**：友好的图形用户界面

## 使用方法

### 1. 选择图片文件
- 点击"选择图片文件"按钮
- 可以一次选择多个图片文件
- 支持常见的图片格式

### 2. 设置输出选项
- **输出文件夹**：选择压缩后图片的保存位置
- **输出格式**：选择输出的图片格式（JPEG/PNG/WEBP）

### 3. 调整压缩设置
- **压缩质量**：使用滑块调整压缩质量（10%-100%）
  - 质量越低，文件越小，但图片质量会下降
  - 质量越高，文件越大，但图片质量更好

### 4. 尺寸调整（可选）
- 勾选"启用尺寸调整"
- 设置目标宽度和高度
- 可选择是否保持宽高比

### 5. 开始处理
- 点击"开始压缩"按钮
- 程序会显示处理进度
- 完成后可选择打开输出文件夹

## 技术特点

- **多线程处理**：不会阻塞用户界面
- **内存优化**：逐个处理图片，避免内存溢出
- **错误处理**：单个文件出错不影响其他文件处理
- **格式兼容**：自动处理不同格式的颜色模式转换

## 注意事项

1. 输出文件名会自动添加"_compressed"后缀
2. 如果输出文件夹中存在同名文件，会被覆盖
3. 建议在处理大量图片前先测试少量文件
4. JPEG格式不支持透明度，PNG/WEBP支持透明度

## 系统要求

- Windows 7/8/10/11
- 无需安装Python环境（exe版本）

## 版本信息

- 版本：1.0
- 开发语言：Python
- 界面框架：tkinter
- 图像处理：Pillow (PIL)

---

如有问题或建议，请联系开发者。
"""
    
    with open("README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("创建README.md文件完成！")

def main():
    """主函数"""
    print("=" * 50)
    print("图片压缩工具打包脚本")
    print("=" * 50)
    
    # 检查主程序文件是否存在
    if not os.path.exists("image_compressor.py"):
        print("错误: 找不到主程序文件 image_compressor.py")
        return
    
    # 创建说明文件
    create_readme()
    
    # 安装依赖
    if not install_requirements():
        print("依赖安装失败，无法继续打包")
        return
    
    # 打包exe
    if build_exe():
        print("\n" + "=" * 50)
        print("打包成功！")
        print(f"exe文件位置: {os.path.abspath('dist/图片压缩工具.exe')}")
        print("=" * 50)
        
        # 询问是否清理临时文件
        try:
            choice = input("\n是否清理临时文件？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                clean_build_files()
                print("清理完成！")
        except KeyboardInterrupt:
            print("\n用户取消操作")
    else:
        print("打包失败！")

if __name__ == "__main__":
    main()
