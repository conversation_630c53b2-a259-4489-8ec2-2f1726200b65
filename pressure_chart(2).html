<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>压力监测双折线图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      #chart {
        width: 100%;
        height: 600px;
      }
      .title {
        text-align: center;
        margin-bottom: 20px;
        color: #333;
      }
    </style>
  </head>
  <body>
    <h1 class="title">压力监测数据可视化</h1>
    <div id="chart"></div>

    <script>
      const newestData = {
        '2025-09-05': {
          采集时间: '2025-09-05 00:00:10,2025-09-05 00:00:20,2025-09-05 00:00:30',
          出口压力: '1.0392,1.0374,1.0485',
          末端压力: '0.302,0.302,0.302'
        },
        '2025-09-06': {
          采集时间: '2025-09-06 00:00:10,2025-09-06 00:00:20,2025-09-06 00:00:30',
          出口压力: '1.0392,1.0374,1.0485',
          末端压力: '0.302,0.302,0.302'
        },
        '2025-09-07': {
          采集时间: '2025-09-07 00:00:10,2025-09-07 00:00:20,2025-09-07 00:00:30',
          出口压力: '1.0392,1.0374,1.0485',
          末端压力: '0.302,0.302,0.302'
        }
      }
      var chartDom = document.getElementById('chart')
      var myChart = echarts.init(chartDom)

      var option = {
        title: {
          text: '出口压力与末端压力对比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params) {
            var result = params[0].axisValueLabel + '<br/>'
            params.forEach(function (item) {
              result += item.marker + item.seriesName + ': ' + item.value + (item.seriesName === '出口压力' ? ' MPa' : item.seriesName === '末端压力' ? ' MPa' : item.seriesName === '用水量' ? '吨' : ' Hz') + '<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['出口压力', '末端压力'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '压力 (MPa)',
            position: 'left',
            axisLabel: {
              formatter: '{value} MPa'
            },
            nameTextStyle: {
              color: '#5470c6'
            }
          }
        ],

        series: [
          {
            name: '出口压力',
            type: 'line',
            data: [],
            smooth: true,
            lineStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '末端压力',
            type: 'line',
            data: [],
            smooth: true,
            lineStyle: {
              color: '#91cc75'
            }
          }
        ]
      }

      myChart.setOption(option)

      // 响应式调整
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    </script>
  </body>
</html>
