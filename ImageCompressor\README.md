# 图片压缩工具

## 🚀 快速开始

### 方法一：直接运行Python脚本
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行程序
python image_compressor.py
```

### 方法二：打包成exe文件
```bash
# 使用Python脚本打包
python build.py

# 或使用批处理文件打包
build.bat
```

## 📋 功能详解

### 1. 文件选择
- **支持格式**：JPEG, PNG, BMP, GIF, TIFF, WEBP
- **批量选择**：可一次选择多个文件
- **文件预览**：显示已选择的文件列表

### 2. 输出设置
- **输出文件夹**：指定压缩后文件的保存位置
- **输出格式**：
  - **JPEG**：适合照片，文件小，不支持透明度
  - **PNG**：适合图标、截图，支持透明度，文件较大
  - **WEBP**：现代格式，压缩率高，支持透明度

### 3. 压缩控制
- **质量范围**：10% - 100%
- **推荐设置**：
  - 高质量保存：80-95%
  - 平衡质量与大小：60-80%
  - 最大压缩：10-40%

### 4. 尺寸调整
- **启用条件**：勾选"启用尺寸调整"
- **调整方式**：
  - 保持宽高比：按比例缩放，不变形
  - 强制尺寸：拉伸到指定尺寸，可能变形
- **常用尺寸**：
  - 4K：3840×2160
  - 2K：2560×1440
  - 1080P：1920×1080
  - 720P：1280×720

## 🎯 使用技巧

### 压缩策略
1. **照片压缩**：
   - 格式：JPEG
   - 质量：70-85%
   - 尺寸：根据用途调整

2. **网页图片**：
   - 格式：WEBP（现代浏览器）或JPEG
   - 质量：60-80%
   - 尺寸：适合网页显示

3. **图标/Logo**：
   - 格式：PNG
   - 质量：90-100%
   - 保持原始尺寸或等比缩放

### 批量处理建议
1. **分批处理**：大量文件建议分批处理
2. **备份原文件**：处理前备份重要文件
3. **测试设置**：先用少量文件测试压缩效果

## ⚠️ 注意事项

### 文件处理
- 输出文件名自动添加"_compressed"后缀
- 同名文件会被覆盖，请注意备份
- 处理失败的文件会跳过，不影响其他文件

### 格式转换
- JPEG不支持透明度，透明区域会变成白色
- PNG转JPEG时会丢失透明度信息
- 建议保留原文件作为备份

### 性能优化
- 程序使用多线程，不会卡死界面
- 大文件处理时间较长，请耐心等待
- 内存占用已优化，逐个处理文件

## 🔧 打包说明

### 环境要求
- Python 3.7+
- pip包管理器

### 打包步骤
1. **安装依赖**：
   ```bash
   pip install Pillow pyinstaller
   ```

2. **执行打包**：
   ```bash
   # 方法1：使用Python脚本
   python build.py
   
   # 方法2：使用批处理文件
   build.bat
   
   # 方法3：直接使用PyInstaller
   pyinstaller --onefile --windowed --name=图片压缩工具 image_compressor.py
   ```

3. **获取exe文件**：
   - 文件位置：`dist/图片压缩工具.exe`
   - 可独立运行，无需Python环境

### 打包参数说明
- `--onefile`：打包成单个exe文件
- `--windowed`：不显示控制台窗口
- `--name`：指定exe文件名
- `--icon`：指定图标文件（可选）

## 🐛 常见问题

### Q1：程序无法启动
**A1**：检查是否安装了所需依赖包，运行 `pip install -r requirements.txt`

### Q2：处理大文件时程序卡死
**A2**：程序使用多线程，界面不会卡死。大文件处理时间较长，请耐心等待。

### Q3：压缩后文件反而变大
**A3**：可能原因：
- 原文件已经高度压缩
- 质量设置过高
- 格式转换导致（如PNG转JPEG）

### Q4：透明背景变成白色
**A4**：JPEG格式不支持透明度，建议使用PNG或WEBP格式。

### Q5：exe文件太大
**A5**：PyInstaller会打包Python解释器，文件较大是正常的。可以考虑使用其他打包工具如cx_Freeze。

## 📞 技术支持

如遇到问题或有改进建议，请：
1. 检查本说明文档
2. 确认Python和依赖包版本
3. 提供详细的错误信息

---

**版本**：1.0  
**更新日期**：2024年  
**开发语言**：Python 3.x  
**界面框架**：tkinter  
**图像处理**：Pillow (PIL)
